import logging
import pandas as pd
import talib.abstract as ta
from typing import Optional,Tuple
from freqtrade.persistence import Trade
from datetime import datetime, timedelta
from freqtrade.strategy import informative
from freqtrade.strategy.interface import IStrategy

logger = logging.getLogger(__name__)
# 这个版本主要用于开发

class Long_1H_Dev(IStrategy):
    INTERFACE_VERSION = 3               # freqtrade策略接口版本号
    timeframe = '1h'                    # 策略运行的时间周期
    startup_candle_count = 12           # 策略启动需要的历史K线数量
    minimal_roi = {"0": 100}            # ROI：设置为100%意味着禁用传统ROI
    MAX_LEVERAGE = 10.0                 # 最大杠杆倍数
    max_open_trades = 50                # 最大同时持仓数量
    stoploss = -1                       # 固定止损设置
    trailing_stop = False               # 禁用跟踪止损
    trailing_stop_positive = None       # 禁用跟踪止损的盈利触发点
    use_custom_stoploss = True          # 使用自定义止损
    stoploss_on_exchange = True         # 在交易所层面执行自定义止损
    use_exit_signal = True              # 是否使用退出信号
    exit_profit_only = False            # 是否只在盈利时退出
    can_short = False                   # 是否允许做空交易：否

    def __init__(self, config: dict) -> None:
        super().__init__(config)

        self.total_stake = 1000             # 总仓位基数
        self.initial_stoploss_info = {}     # 存储建仓止损信息
        self.take_profit_prices = {}        # 存储每个交易的移动止盈价格
        self.market_indicators = {}         # 存储全市场指标（排名和平均值）

    def informative_pairs(self):
        """定义需要获取的交易对信息"""
        pairs = self.dp.current_whitelist()     # 获取当前白名单中的所有交易对，为主时间周期添加所有交易对，这是必需的
        informative_pairs = []                  # 用于存储需要获取的交易对信息
        informative_pairs.extend([(pair, self.timeframe) for pair in pairs])        # 为主时间周期添加所有交易对
        informative_pairs.append(('ETH/USDT:USDT', '1h'))                           # 添加ETH/USDT:USDT的1小时K线数据
        active_trades = Trade.get_open_trades()
        active_pairs = set(trade.pair for trade in active_trades)
        if active_pairs:
            informative_pairs.extend([(pair, '1m') for pair in active_pairs])       # 为活跃交易对添加1分钟K线数据
            informative_pairs.extend([(pair, '3m') for pair in active_pairs])       # 为活跃交易对添加3分钟K线数据
            informative_pairs.extend([(pair, '5m') for pair in active_pairs])       # 为活跃交易对添加5分钟K线数据
            informative_pairs.extend([(pair, '15m') for pair in active_pairs])      # 为活跃交易对添加15分钟K线数据
        return informative_pairs

    @informative('1h', 'ETH/USDT:USDT', fmt="{asset}_{column}_{timeframe}")
    def populate_eth_indicators(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """计算ETH/USDT:USDT的increase_utc_cumsum指标"""
        dataframe = dataframe.sort_values('date', ascending=True)
        dataframe['increase_1h'] = dataframe['close'].pct_change(1) * 100
        dataframe['utc_hour'] = dataframe['date'].dt.hour
        dataframe['utc_cumsum'] = dataframe.groupby(dataframe['date'].dt.date)['increase_1h'].cumsum()
        dataframe['ema_10'] = ta.EMA(dataframe['close'], timeperiod=10)
        return dataframe

    @informative('4h')
    def populate_indicators_4h(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """计算4h级别的指标"""
        # 计算4h级别的连续下跌天数
        dataframe['consecutive_4h'] = dataframe['close'] >= dataframe['open']  # 标记下跌日
        dataframe['consecutive_4h_count'] = dataframe['consecutive_4h'].cumsum() - dataframe['consecutive_4h'].cumsum().where(~dataframe['consecutive_4h']).ffill().fillna(0)
        dataframe['ema_25'] = ta.EMA(dataframe['close'], timeperiod=25)
        dataframe['ema_50'] = ta.EMA(dataframe['close'], timeperiod=50)
        dataframe['ema_100'] = ta.EMA(dataframe['close'], timeperiod=100)
        dataframe['ema_150'] = ta.EMA(dataframe['close'], timeperiod=150)
        dataframe['ema_200'] = ta.EMA(dataframe['close'], timeperiod=200)
        return dataframe

    @informative('8h', candle_type='funding_rate')
    def populate_funding_rate(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        dataframe['funding_rate'] = dataframe['open'].round(8)          # 资金费率一般在 open 列
        return dataframe

    @informative('1d')
    def populate_indicators_1d(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """计算日线级别的指标"""
        dataframe['consecutive_day'] = dataframe['close'] <= dataframe['open']  # 标记下跌日
        dataframe['consecutive_day_count'] = dataframe['consecutive_day'].cumsum() - dataframe['consecutive_day'].cumsum().where(~dataframe['consecutive_day']).ffill().fillna(0)
        dataframe['increase_day'] = dataframe['close'].pct_change(1) * 100       # 1天的K线的涨幅
        return dataframe

    def _calculate_basic_indicators(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """计算单个交易对技术指标"""
        dataframe = dataframe.sort_values('date', ascending=True)                   # 确保数据按时间排序
        dataframe['increase_1h'] = dataframe['close'].pct_change(1) * 100           # 1小时K线的涨幅
        dataframe['increase_3h'] = dataframe['increase_1h'].rolling(3).sum()        # 3小时K线的涨幅
        dataframe['increase_4h'] = dataframe['increase_1h'].rolling(4).sum()        # 4小时K线的涨幅
        dataframe['increase_6h'] = dataframe['increase_1h'].rolling(6).sum()        # 6小时K线的涨幅
        dataframe['k1_increase'] = dataframe['increase_1h'].shift(0)                # 当前时间的前一根k线涨幅
        dataframe['volume_$'] = dataframe['volume'] * dataframe['close']            # 成交量乘以收盘价
        dataframe['volume_24h'] = dataframe['volume_$'].rolling(24).sum()           # 24小时成交量
        
        # 实现灵活区间的最高价指标
        dataframe['interval_max_12'] = dataframe['high'].shift(6).rolling(window=6, min_periods=1).max()           # 排除最近7小时最高价后的22小时最高价
        dataframe['interval_max_22'] = dataframe['high'].shift(5).rolling(window=14, min_periods=1).max()           # 排除最近7小时最高价后的22小时最高价
        dataframe['interval_max_22_ratio'] = (dataframe['high'] - dataframe['interval_max_22']) / dataframe['high']
        dataframe['interval_max_36'] = dataframe['high'].shift(7).rolling(window=28, min_periods=1).max()           # 排除最近7小时最高价后的36小时最高价
        dataframe['interval_max_36_ratio'] = (dataframe['high'] - dataframe['interval_max_36']) / dataframe['high']
        dataframe['interval_max_48'] = dataframe['high'].shift(7).rolling(window=40, min_periods=1).max()           # 排除最近7小时最高价后的48小时最高价
        dataframe['interval_max_48_ratio'] = (dataframe['high'] - dataframe['interval_max_48']) / dataframe['high']
        dataframe['interval_max_72'] = dataframe['high'].shift(14).rolling(window=64, min_periods=1).max()           # 排除最近7小时最高价后的48小时最高价
        dataframe['interval_max_72_ratio'] = (dataframe['high'] - dataframe['interval_max_72']) / dataframe['high']

        # 计算技术指标
        dataframe['ema_25'] = ta.EMA(dataframe['close'], timeperiod=25)
        dataframe['ema_50'] = ta.EMA(dataframe['close'], timeperiod=50)
        dataframe['ema_100'] = ta.EMA(dataframe['close'], timeperiod=100)
        dataframe['adx_14'] = ta.ADX(dataframe['high'], dataframe['low'], dataframe['close'], timeperiod=14)

        # UTC0时间24小时累计涨幅
        dataframe['utc_hour'] = dataframe['date'].dt.hour
        dataframe['increase_utc_cumsum'] = dataframe.groupby(dataframe['date'].dt.date)['increase_1h'].cumsum()

        # 统计过去7天内是否有出现过涨幅>15%的次数
        dataframe['exceed_15pct'] = (dataframe['increase_utc_cumsum'] <= -25).astype(int)          # 标记累计涨幅超过15%的时刻
        dataframe['increase_utc_15pct_7d'] = dataframe['exceed_15pct'].rolling(window=48, min_periods=1).sum()        # 使用7天(168小时)的滚动窗口检查超过15%的次数

        # 计算连续上涨K线数量 - 基于1小时K线
        dataframe = self._calculate_consecutive_ups(dataframe)
        return dataframe

    def _calculate_consecutive_ups(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算连续上涨K线，使用最简单的涨幅判断，并统计连续期间的涨幅数据"""
        dataframe = df.copy()
        dataframe['consecutive_hour'] = dataframe['close'] > dataframe['open']
        dataframe['consecutive_hour_count'] = dataframe['consecutive_hour'].cumsum() - dataframe['consecutive_hour'].cumsum().where(~dataframe['consecutive_hour']).ffill().fillna(0)

        # 计算连续期间的涨幅数据
        dataframe['consecutive_increase'] = 0.0
        dataframe['group_id'] = (~dataframe['consecutive_hour']).cumsum()                                   # 创建分组标识：每当连续上涨中断时，分组ID增加
        for group_id, group in dataframe[dataframe['consecutive_hour']].groupby('group_id'):
            if len(group) > 0:
                cumulative_increase = group['increase_1h'].cumsum()                                         # 计算累进涨幅
                dataframe.loc[group.index, 'consecutive_increase'] = cumulative_increase                    # 将累进涨幅赋值给数据框
        
        dataframe = dataframe.drop('group_id', axis=1)
        return dataframe

    def _calculate_market_indicators(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """计算市场指标 - 利用大表和向量化批量计算"""
        pair = metadata['pair']
        market_columns = [
            'consecutive_hour_count_sum', 'rank_consecutive', 'rank_4h', 'rank_6h', 'rank_utc',
            'avg_increase_1h', 'avg_increase_4h',  'above_ema100_ratio'
        ]
        dataframe = dataframe.assign(**{col: 0 for col in market_columns})

        # 获取pairs，异常时给空列表
        try:
            pairs = self.dp.current_whitelist() if hasattr(self, 'dp') and hasattr(self.dp, 'current_whitelist') else []
        except Exception as e:
            msg = f"获取白名单失败: {e}"
            logger.warning(msg)
            pairs = []

        # 非首pair直接用缓存
        if pairs and pair != pairs[0] and hasattr(self, 'market_indicators') and pair in self.market_indicators:
            date_mapping = self.market_indicators[pair]
            dataframe[market_columns] = dataframe['date'].map(lambda d: {col: date_mapping.get(d, {}).get(col, 0) for col in market_columns}).apply(pd.Series)
            return dataframe

        # 先缓存所有pair的df，避免重复调用
        pair_dfs = {p: self.dp.get_pair_dataframe(pair=p, timeframe=self.timeframe) for p in pairs}
        all_dfs = [
            self._calculate_basic_indicators(df.copy(), metadata={}).assign(pair=p)
            for p, df in pair_dfs.items() if df is not None and not df.empty
        ]
        if not all_dfs:
            return dataframe

        all_data = pd.concat(all_dfs, axis=0, ignore_index=True)

        # 计算价格在不同EMA上方的交易对占总交易对的比例
        total_pairs = len(pairs)  # 获取总交易对数量
        if total_pairs > 0:
            for period in [100]:
                above_ema_pairs = all_data.groupby('date').apply(lambda x: (x['close'] > x[f'ema_{period}']).sum())
                all_data[f'above_ema{period}_ratio'] = all_data['date'].map(lambda d: above_ema_pairs.get(d, 0) / total_pairs)

        avg_exclude_extreme = lambda x: x.sort_values(ascending=False).iloc[4:50].mean() if len(x) >= 50 else 0
        avg_decrease_only = lambda x: x[x < 0].mean() if (x < 0).any() else 0
        all_data['avg_increase_1h'] = all_data.groupby('date')['increase_1h'].transform(avg_exclude_extreme)
        all_data['avg_increase_4h'] = all_data.groupby('date')['increase_4h'].transform(avg_exclude_extreme)

        all_data['rank_4h'] = all_data.groupby('date')['increase_4h'].rank(ascending=False)                     # 按照4小时涨幅计算排名
        all_data['rank_6h'] = all_data.groupby('date')['increase_6h'].rank(ascending=False)                     # 按照6小时涨幅计算排名
        all_data['rank_utc'] = all_data.groupby('date')['increase_utc_cumsum'].rank(ascending=False)            # 按照utc0时间24小时累计涨幅计算排名

        mask_consecutive_hour_count = (all_data['consecutive_hour_count'] == 3) 
        all_data['consecutive_hour_xx'] = mask_consecutive_hour_count.astype(int)
        all_data['consecutive_hour_count_sum'] = all_data.groupby('date')['consecutive_hour_xx'].transform('sum')
        all_data['rank_consecutive'] = 0
        for date, group in all_data[mask_consecutive_hour_count].groupby('date'):
            idx = group.sort_values('increase_3h', ascending=False).index                                       # 按连某参数的涨幅排序
            all_data.loc[idx, 'rank_consecutive'] = range(1, len(idx) + 1)

        self.market_indicators = {
            p: df.groupby('date')[market_columns].apply(lambda g: {col: g[col].iloc[0] if col in g.columns else 0 for col in market_columns}).to_dict()
            for p, df in all_data.groupby('pair')                                                               # 按照交易对分组
        }
        if pair in self.market_indicators:                                                                      # 如果当前交易对在市场指标字典中
            date_mapping = self.market_indicators[pair]                                                         # 获取当前交易对的市场指标
            dataframe[market_columns] = dataframe['date'].map(                                                  # 将市场指标映射到当前交易对
                lambda d: {col: date_mapping.get(d, {}).get(col, 0) for col in market_columns}                  # 如果当前交易对的市场指标不存在，则返回0
            ).apply(pd.Series)
        return dataframe

    def populate_indicators(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """调用市场指标和单个交易对技术指标"""

        dataframe = self._calculate_market_indicators(dataframe, metadata)  # 计算市场指标
        dataframe = self._calculate_basic_indicators(dataframe, metadata)   # 计算基础指标
        return dataframe

    def populate_entry_trend(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        # 初始化信号列
        dataframe['enter_long'] = 0
        dataframe['enter_tag'] = None

        dataframe.loc[
            (
                (dataframe['rank_consecutive'] >= 1) & (dataframe['rank_consecutive'] <= 3) &
                (dataframe['eth/usdt:usdt_close_1h'] > dataframe['eth/usdt:usdt_ema_10_1h']) &
                (dataframe['increase_3h'] >= 1.0) &
                (dataframe['volume_24h'] >= 10000000)
            ),
            ['enter_long', 'enter_tag']
        ] = (1, 'BUY_RISE_1')
        return dataframe

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                max_leverage: float, entry_tag: Optional[str],
                side: str, **kwargs) -> float:
        """返回交易所支持的最大杠杆，但限制上限为15倍，这样可以使用最少的保证金，而实际使用的杠杆是由position size决定的"""
        return min(max_leverage, self.MAX_LEVERAGE)

    def custom_stake_amount(self, pair: str, current_time: datetime, current_rate: float,
                          proposed_stake: float, min_stake: Optional[float], max_stake: float,
                          leverage: float, entry_tag: Optional[str], side: str,
                          **kwargs) -> float:
        """计算实际下单金额，返回的是保证金金额"""

        # 直接用名义金额/杠杆得到保证金金额
        stake_amount = self.total_stake / leverage if leverage > 0 else 0        # 保证金金额

        # 确保下单金额在允许范围内
        if min_stake:
            stake_amount = max(min_stake, stake_amount)
        stake_amount = min(max_stake, stake_amount)
        return stake_amount

    def custom_stoploss(self, pair: str, trade: Trade, current_time: datetime,
                       current_rate: float, current_profit: float,
                       **kwargs) -> float:
        """基于首次进入价格的绝对价格止损功能（仅做多方向）"""
        entry_price = trade.open_rate                                   # 第一个订单的成交均价
        stop_price = entry_price * (1 - 0.1)                            # 计算止损价格（首次进入价格的-10%）
        stoploss = (current_rate - stop_price) / current_rate           # 只做多方向，计算止损比例
        stoploss = stoploss * trade.leverage                            # 如果有杠杆，需要调整止损比例

        return stoploss

    

    def populate_exit_trend(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """生成卖出信号"""
        dataframe['exit_long'] = 0
        return dataframe

    def custom_exit(self, pair: str, trade: Trade, current_time: datetime, current_rate: float,
                   current_profit: float, **kwargs) -> Optional[str]:

        trade_id = str(trade.id)  # 确保trade_id是字符串
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)     # 获取分析数据
        if len(dataframe) == 0:
            return None

        """建仓止损"""
        # 筛选开仓前3小时数据
        hour_end = current_time.replace(minute=0, second=0, microsecond=0)
        period_df = dataframe[(dataframe['date'] >= hour_end - timedelta(hours=3)) & (dataframe['date'] < hour_end)].copy()
        if len(period_df) == 0:
            return None

        all_hours_low = period_df['low'].min()                                                              # 计算最低点
        period_df.loc[:, 'hour'] = period_df['date'].dt.floor('h')                                          # 按小时分组
        hour_stats = period_df.groupby('hour').agg({'low': 'min', 'increase_1h': 'last'}).reset_index()     # 按小时统计涨幅和最低点
        hour_stats = hour_stats.sort_values('hour', ascending=False).head(3)                                # 取涨幅最高的3个小时
        valid_hours = hour_stats[hour_stats['increase_1h'] > 1.2]                                           # 取涨幅大于1.2%的小时
        stop_price = valid_hours.iloc[0]['low'] if not valid_hours.empty else all_hours_low                 # 确定止损点
        stop_price_with_buffer = stop_price * 0.995                                                         # 止损点乘0.995

        # 存储建仓止损价格（如果尚未存储）
        if trade.id not in self.initial_stoploss_info:
            self.initial_stoploss_info[trade.id] = {
                'price': stop_price_with_buffer,
                'raw_price': stop_price,
                'time': current_time
            }

        # 判断是否触发退出
        if trade.id in self.initial_stoploss_info and current_rate <= self.initial_stoploss_info[trade.id]['price']:
            logger.info(f"{pair} - 触发建仓止损: 当前价格({current_rate}) <= 止损价格({self.initial_stoploss_info[trade.id]['price']})")
            return '建仓止损'

        """移动止盈"""
        # 检查是否已经设置了移动止盈并判断是否触发
        take_profit_price = self.take_profit_prices.get(trade.id)
        if take_profit_price is not None and current_rate <= take_profit_price:
            return '移动止盈'

        # 获取最近一个完整小时的数据用于移动止盈判断
        if len(dataframe) > 1:
            if dataframe.iloc[-1]['increase_1h'] >= 1.02 and dataframe.iloc[-1]['low'] >= trade.open_rate:           # 判断条件: 小时涨幅 > 1.05% 且 最低点 >= 持仓均价
                new_take_profit = dataframe.iloc[-1]['low'] * 0.995                                                  # 最低点-0.5%
                if take_profit_price is None or new_take_profit > take_profit_price:                                 # 如果已有移动止盈价格，确保新价格更高
                    self.take_profit_prices[trade.id] = new_take_profit
                    if current_rate <= new_take_profit:
                        logger.info(f"{pair} - 立即触发移动止盈: 当前价格({current_rate}) <= 移动止盈点({new_take_profit})")
                        return '移动止盈'

        return None
